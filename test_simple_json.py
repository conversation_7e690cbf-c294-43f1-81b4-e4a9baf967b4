#!/usr/bin/env python3
"""
Test simple pour vérifier l'export JSON avec masque.
"""

import os
import sys
import cv2
import numpy as np
import json

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.json_service import JsonExporter


def test_export_mask_to_json():
    """Test l'export JSON depuis un masque simulé."""
    print("=== Test export JSON depuis masque ===")
    
    try:
        # Créer un masque de test simple
        h, w = 500, 500
        test_mask = np.zeros((h, w), dtype=np.uint8)
        
        # Ajouter quelques formes
        # Frontwall (valeur 1) - rectangle
        test_mask[100:200, 100:300] = 1
        
        # Backwall (valeur 2) - rectangle
        test_mask[250:350, 150:350] = 2
        
        # Flaw (valeur 3) - petit carré
        test_mask[400:450, 400:450] = 3
        
        print(f"[OK] Masque de test créé: {h}x{w}")
        
        # Vérifier le contenu du masque
        unique_values = np.unique(test_mask)
        print(f"[INFO] Valeurs dans le masque: {unique_values}")
        
        for val in unique_values:
            if val > 0:
                count = np.sum(test_mask == val)
                label_name = {1: 'frontwall', 2: 'backwall', 3: 'flaw', 4: 'indication'}.get(val, f'unknown({val})')
                print(f"   • {label_name}: {count} pixels")
        
        # Créer un modèle simulé
        class MockModel:
            def __init__(self):
                self.current_index = 0
                self.image_list = ["test_image.png"]
        
        model = MockModel()
        
        # Paramètres de test
        label_settings = {
            'frontwall': {'threshold': 128, 'mask_type': 'standard'},
            'backwall': {'threshold': 128, 'mask_type': 'standard'},
            'flaw': {'threshold': 128, 'mask_type': 'standard'},
            'indication': {'threshold': 128, 'mask_type': 'standard'}
        }
        
        # Créer l'exporteur JSON
        json_exporter = JsonExporter()
        
        # Créer le dossier de sortie
        output_dir = "test_exports"
        os.makedirs(output_dir, exist_ok=True)
        json_path = os.path.join(output_dir, "test_mask_export.json")
        
        # Exporter le masque vers JSON
        print(f"\n[TEST] Export du masque vers JSON...")
        result_path = json_exporter.export_mask_to_json(
            test_mask, model, label_settings, json_path
        )
        
        if result_path and os.path.exists(result_path):
            print(f"[OK] JSON exporté avec succès: {result_path}")
            
            # Lire et vérifier le contenu
            with open(result_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            print(f"[INFO] Contenu du JSON:")
            for image_key, image_data in json_data.items():
                annotations = image_data.get('OBJECT_DETECTION_JOB', {}).get('annotations', [])
                print(f"   • Image {image_key}: {len(annotations)} annotation(s)")
                
                for i, annotation in enumerate(annotations):
                    categories = annotation.get('categories', [])
                    if categories:
                        category_name = categories[0].get('name', 'Unknown')
                        vertices = annotation.get('boundingPoly', [{}])[0].get('normalizedVertices', [])
                        print(f"     - Annotation {i+1}: {category_name} ({len(vertices)} points)")
            
            # Afficher le JSON complet pour debug
            print(f"\n[DEBUG] JSON complet:")
            print(json.dumps(json_data, indent=2, ensure_ascii=False))
            
            return len(json_data.get('0', {}).get('OBJECT_DETECTION_JOB', {}).get('annotations', [])) > 0
        else:
            print(f"[ERREUR] Échec de l'export JSON")
            return False
            
    except Exception as e:
        print(f"[ERREUR] Exception: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("Test simple de l'export JSON depuis masque")
    print("=" * 50)
    
    success = test_export_mask_to_json()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Test réussi ! L'export JSON depuis masque fonctionne.")
    else:
        print("❌ Test échoué. Vérifiez les erreurs ci-dessus.")
