#!/usr/bin/env python3
"""
Script de test pour vérifier que l'export JSON fonctionne maintenant.
"""

import os
import sys
import cv2
import numpy as np
import json
from pathlib import Path

# Ajouter le répertoire racine au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.mask_model import MaskModel
from services.export_service import export_annotations_to_json, _generate_individual_mask_for_export
from services.json_service import JsonExporter


def test_json_export_with_polygons():
    """Test l'export JSON avec des polygones simulés."""
    print("=== Test de l'export JSON avec polygones ===")
    
    try:
        # Créer un modèle de test avec une image
        test_image_path = "tests/endview_000000019000.png"
        
        if not os.path.exists(test_image_path):
            print(f"[ERREUR] Image de test introuvable: {test_image_path}")
            return False
        
        # Initialiser le modèle
        model = MaskModel([test_image_path])
        model.load_current_image()
        
        print(f"[OK] Image chargée: {model.current_filename()}")
        print(f"[INFO] Dimensions: {model.image_original.shape}")
        
        # Ajouter des polygones de test avec paramètres
        test_polygons = {
            'frontwall': [
                {
                    'points': [(150, 150), (250, 150), (250, 250), (150, 250), (150, 150)],
                    'parameters': {'threshold': 120, 'mask_type': 'polygon', 'smooth_contours': False}
                }
            ],
            'backwall': [
                {
                    'points': [(300, 300), (400, 300), (400, 400), (300, 400), (300, 300)],
                    'parameters': {'threshold': 100, 'mask_type': 'standard', 'smooth_contours': False}
                }
            ]
        }
        
        # Ajouter les polygones au modèle
        for label, polygons in test_polygons.items():
            for polygon_data in polygons:
                model.add_polygon(label, polygon_data['points'], polygon_data['parameters'])
        
        print(f"[OK] Polygones ajoutés au modèle")
        
        # Vérifier que les polygones sont bien dans le modèle
        all_polygons = model.get_all_polygons()
        total_polygons = sum(len(polys) for polys in all_polygons.values())
        print(f"[INFO] Nombre total de polygones dans le modèle: {total_polygons}")
        
        for label, polygons in all_polygons.items():
            if polygons:
                print(f"   • {label}: {len(polygons)} polygone(s)")
        
        # Tester la génération du masque
        print("\n[TEST] Génération du masque final...")
        label_settings = {
            'frontwall': {'threshold': 120, 'mask_type': 'polygon', 'smooth_contours': False},
            'backwall': {'threshold': 100, 'mask_type': 'standard', 'smooth_contours': False},
            'flaw': {'threshold': 128, 'mask_type': 'standard', 'smooth_contours': False},
            'indication': {'threshold': 128, 'mask_type': 'standard', 'smooth_contours': False}
        }
        
        final_mask = _generate_individual_mask_for_export(model, label_settings)
        
        # Vérifier le masque
        unique_values = np.unique(final_mask)
        print(f"[INFO] Valeurs uniques dans le masque: {unique_values}")
        
        for val in unique_values:
            if val > 0:
                count = np.sum(final_mask == val)
                label_name = {1: 'frontwall', 2: 'backwall', 3: 'flaw', 4: 'indication'}.get(val, f'unknown({val})')
                print(f"   • {label_name}: {count} pixels")
        
        # Tester l'export JSON
        print("\n[TEST] Export JSON...")
        output_dir = "test_exports"
        os.makedirs(output_dir, exist_ok=True)
        
        json_path = export_annotations_to_json(model, label_settings, output_dir)
        
        if json_path and os.path.exists(json_path):
            print(f"[OK] JSON exporté avec succès: {json_path}")
            
            # Vérifier le contenu du JSON
            with open(json_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            print(f"[INFO] Structure JSON:")
            for image_key, image_data in json_data.items():
                annotations = image_data.get('OBJECT_DETECTION_JOB', {}).get('annotations', [])
                print(f"   • Image {image_key}: {len(annotations)} annotation(s)")
                
                for i, annotation in enumerate(annotations):
                    categories = annotation.get('categories', [])
                    if categories:
                        category_name = categories[0].get('name', 'Unknown')
                        vertices = annotation.get('boundingPoly', [{}])[0].get('normalizedVertices', [])
                        print(f"     - Annotation {i+1}: {category_name} ({len(vertices)} points)")
            
            return True
        else:
            print(f"[ERREUR] Échec de l'export JSON")
            return False
            
    except Exception as e:
        print(f"[ERREUR] Exception lors du test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_empty_model():
    """Test l'export JSON avec un modèle vide (cas original du problème)."""
    print("\n=== Test de l'export JSON avec modèle vide ===")
    
    try:
        # Créer un modèle vide
        test_image_path = "tests/endview_000000019000.png"
        
        if not os.path.exists(test_image_path):
            print(f"[ERREUR] Image de test introuvable: {test_image_path}")
            return False
        
        model = MaskModel([test_image_path])
        model.load_current_image()
        
        print(f"[OK] Modèle vide créé")
        
        # Vérifier qu'il n'y a pas de polygones
        all_polygons = model.get_all_polygons()
        total_polygons = sum(len(polys) for polys in all_polygons.values())
        print(f"[INFO] Nombre de polygones: {total_polygons}")
        
        # Tester l'export JSON
        label_settings = {
            'frontwall': {'threshold': 128, 'mask_type': 'standard', 'smooth_contours': False},
            'backwall': {'threshold': 128, 'mask_type': 'standard', 'smooth_contours': False},
            'flaw': {'threshold': 128, 'mask_type': 'standard', 'smooth_contours': False},
            'indication': {'threshold': 128, 'mask_type': 'standard', 'smooth_contours': False}
        }
        
        output_dir = "test_exports"
        json_path = export_annotations_to_json(model, label_settings, output_dir)
        
        if json_path and os.path.exists(json_path):
            print(f"[OK] JSON exporté: {json_path}")
            
            # Vérifier le contenu
            with open(json_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            print(f"[INFO] Contenu JSON pour modèle vide:")
            for image_key, image_data in json_data.items():
                annotations = image_data.get('OBJECT_DETECTION_JOB', {}).get('annotations', [])
                print(f"   • Image {image_key}: {len(annotations)} annotation(s)")
            
            return True
        else:
            print(f"[ERREUR] Échec de l'export JSON pour modèle vide")
            return False
            
    except Exception as e:
        print(f"[ERREUR] Exception lors du test modèle vide: {str(e)}")
        return False


if __name__ == "__main__":
    print("Test de la correction de l'export JSON")
    print("=" * 50)
    
    # Test avec polygones
    success1 = test_json_export_with_polygons()
    
    # Test avec modèle vide
    success2 = test_empty_model()
    
    print("\n" + "=" * 50)
    print("RÉSULTATS:")
    print(f"• Test avec polygones: {'✅ SUCCÈS' if success1 else '❌ ÉCHEC'}")
    print(f"• Test modèle vide: {'✅ SUCCÈS' if success2 else '❌ ÉCHEC'}")
    
    if success1 and success2:
        print("\n🎉 Tous les tests sont passés ! L'export JSON fonctionne maintenant.")
    else:
        print("\n⚠️  Certains tests ont échoué. Vérifiez les erreurs ci-dessus.")
